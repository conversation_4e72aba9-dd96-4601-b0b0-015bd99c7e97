import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_area_controller.dart';

import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class PersistensiWidgetController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team, 2 for Area
  String level = '';
  String agentCode = '';
  String userType = '';
  String channel = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = false.obs;

  // Filter properties
  RxString selectedView = 'my_self_str'.obs;
  RxList<String> selectedBranches = <String>[].obs;

  // Search properties
  RxString searchQuery = ''.obs;
  late TextEditingController searchController;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiAreaController areaController = Get.put(
    PersistensiAreaController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiWidgetController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    userType = prefs.getString(kStorageUserType) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';

    // Initialize search controller
    searchController = TextEditingController();

    if (userType == 'STAFF') {
      selectedSection.value = 1;
    }

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'persistensi_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    searchController.dispose();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';
    _hasInitialized = true;
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections - robust method
  void switchToSection(int index) {
    final availableTabs = getAvailableTabs();
    if (index >= 0 && index < availableTabs.length) {
      selectedSection.value = index;
      selectedView.value = availableTabs[index];
      refreshData();
    }
  }

  // Legacy methods for backward compatibility
  void switchToIndividu() {
    // Find index of 'my_self_str' in available tabs
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere((tab) => tab == 'my_self_str'.tr);
    if (index != -1) {
      switchToSection(index);
    }
  }

  void switchToTeam() {
    // Find index of 'team_str' or 'group_str' in available tabs
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere(
      (tab) => tab == 'team_str'.tr || tab == 'group_str'.tr,
    );
    if (index != -1) {
      switchToSection(index);
    }
  }

  void switchToArea() {
    // Find index of 'cabang_str' in available tabs
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere((tab) => tab == 'cabang_str'.tr);
    if (index != -1) {
      switchToSection(index);
    }
  }

  // Refresh data based on current section and user level
  void refreshData() {
    final query = searchQuery.value.trim();
    final controllerType = getControllerTypeForIndex(selectedSection.value);

    switch (controllerType) {
      case 'individu':
        individuController.fetchPersistensiData(
          searchQuery: query.isEmpty ? null : query,
        );
        break;
      case 'team':
        teamController.fetchPersistensiData(
          searchQuery: query.isEmpty ? null : query,
        );
        break;
      case 'area':
        areaController.fetchPersistensiData(
          branchCodes:
              selectedBranches.isNotEmpty ? selectedBranches.toList() : null,
          searchQuery: query.isEmpty ? null : query,
        );
        break;
    }
    setLoading(false);
  }

  // Search functionality
  void performSearch(String query) {
    searchQuery.value = query;
    refreshData();
  }

  // Clear search
  void clearSearch() {
    searchQuery.value = '';
    refreshData();
  }

  // Get available tabs based on channel and role (same as page logic)
  List<String> getAvailableTabs() {
    if (channel == kUserChannelBan) {
      // BAN channel
      if (level == "BO") {
        return ['my_self_str'.tr];
      } else {
        // ASM and above
        return ['team_str'.tr, 'cabang_str'.tr];
      }
    } else {
      // AGE channel - existing logic
      if (level == kLevelBP) return ['my_self_str'.tr];
      return [
        'my_self_str'.tr,
        level == kLevelBD ? 'group_str'.tr : 'team_str'.tr,
      ];
    }
  }

  // Get the controller type for current tab index
  String getControllerTypeForIndex(int index) {
    final availableTabs = getAvailableTabs();
    if (index >= availableTabs.length) return 'individu';

    final tabName = availableTabs[index];

    if (tabName == 'my_self_str'.tr) {
      return 'individu';
    } else if (tabName == 'team_str'.tr || tabName == 'group_str'.tr) {
      return 'team';
    } else if (tabName == 'cabang_str'.tr) {
      return 'area';
    }

    return 'individu'; // fallback
  }

  // Check if should show tabs (for widget view)
  bool shouldShowTabs() {
    return getAvailableTabs().length > 1;
  }

  // Get appropriate column name based on level and controller type
  String getColumnName(String controllerType) {
    if (controllerType == 'team') {
      // Check user level complete for more specific roles
      final userLevelComplete =
          prefs.getString(kStorageUserLevelComplete) ?? '';

      if (level == kLevelASM) {
        return 'nama_bo_str';
      } else if (userLevelComplete == kUserLevelBanRSM) {
        return 'nama_asm_str';
      } else if (userLevelComplete == kUserLevelBanHOS) {
        return 'nama_rsm_str';
      }
    } else if (controllerType == 'area') {
      return 'nama_cabang_str';
    }

    return 'name_str'.tr; // Default "Nama"
  }

  // Apply filter
  void applyFilter(String view, List<String> branches) {
    selectedView.value = view;
    selectedBranches.assignAll(branches);

    // Update selected section based on view using robust logic
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere((tab) => tab == view);
    if (index != -1) {
      selectedSection.value = index;
    }

    refreshData();
  }

  // Apply branch filter only
  void applyBranchFilter(List<String> branches) {
    selectedBranches.assignAll(branches);
    // Apply the filter to the current controller if it's area type
    final controllerType = getControllerTypeForIndex(selectedSection.value);
    if (controllerType == 'area') {
      // For area/cabang view, apply branch filter with multiple codes
      final query = searchQuery.value.trim();
      areaController.fetchPersistensiData(
        branchCodes: branches.isNotEmpty ? branches : null,
        searchQuery: query.isEmpty ? null : query,
      );
    }
  }

  // Reset filter
  void resetFilter() {
    final availableTabs = getAvailableTabs();
    if (availableTabs.isNotEmpty) {
      selectedView.value = availableTabs.first;
      selectedSection.value = 0;
    }
    selectedBranches.clear();
    // Reset area controller filter
    areaController.fetchPersistensiData();
    refreshData();
  }
}
