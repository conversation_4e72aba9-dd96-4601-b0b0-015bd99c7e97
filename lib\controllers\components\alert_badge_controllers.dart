import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/response/count_unread_inbox_response.dart';

class AlertBadgeControllers extends BaseControllers {
  RxInt count = 0.obs;
  RxList<CountUnreadInboxResponse> notificationCounts =
      <CountUnreadInboxResponse>[].obs;

  @override
  void onInit() {
    super.onInit();
    getCountUnreadInbox();
  }

  getCountUnreadInbox() async {
    await api.getCountUnreadInbox(controllers: this, code: 0);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    parseData(response);
  }

  parseData(response) {
    // Parse response dan hitung total count notification
    int totalCount = 0;
    notificationCounts.clear();

    if (response is List) {
      for (final item in response) {
        final countData = CountUnreadInboxResponse.fromJson(item);
        notificationCounts.add(countData);
        totalCount += countData.count ?? 0;
      }
    }

    // Update count dengan total dari semua trxType
    count.value = totalCount;
  }

  /// Mendapatkan count untuk trxType tertentu
  int getCountByTrxType(String trxType) {
    final found = notificationCounts.firstWhereOrNull(
      (element) => element.trxType == trxType,
    );
    return found?.count ?? 0;
  }

  /// Refresh data notification count
  void refreshNotificationCount() {
    getCountUnreadInbox();
  }
}
