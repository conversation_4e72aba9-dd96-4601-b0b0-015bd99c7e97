import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

class PersistensiAreaController extends BaseControllers {
  // Changed to store a list of persistence data for multiple areas
  RxList<PersistensiModel> persistensiDataList = <PersistensiModel>[].obs;
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  ScrollController scrollController = ScrollController();

  // Pagination properties
  int page = 0;
  int maxPage = 0;
  int limit = 50;
  bool enableLoadMore = true;
  RxBool isLoadingMore = false.obs;

  // Filter properties
  String? selectedBranchCode;
  List<String> selectedBranchCodes = [];

  @override
  void onInit() {
    super.onInit();
    setupScrollListener();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 200 &&
          !isLoadingMore.value &&
          enableLoadMore &&
          page < maxPage) {
        loadMoreData();
      }
    });
  }

  void loadMoreData() {
    if (page < maxPage && !isLoadingMore.value) {
      page++;
      isLoadingMore.value = true;
      fetchPersistensiData();
    }
  }

  fetchPersistensiData({
    String? branchCode,
    List<String>? branchCodes,
    String? searchQuery,
  }) async {
    if (page == 0) {
      setLoading(true);
      persistensiDataList.clear();
    }

    hasError.value = false;
    errorMessage.value = '';

    prefs = await SharedPreferences.getInstance();
    String agentCode = prefs.getString(kStorageAgentCode) ?? '';
    // Get agent code from URL parameter if available
    if (Get.parameters.containsKey('agentCode')) {
      agentCode = Get.parameters['agentCode'] ?? '';
    }
    final currentYear = DateTime.now().year.toString();

    if (agentCode.isEmpty) {
      hasError.value = true;
      errorMessage.value = 'Agent code not found';
      setLoading(false);
      isLoadingMore.value = false;
      return;
    }

    // Update selected branch codes if provided
    if (branchCodes != null) {
      selectedBranchCodes = branchCodes;
      page = 0; // Reset pagination when filtering
      persistensiDataList.clear();
      setLoading(true);
    } else if (branchCode != null) {
      selectedBranchCode = branchCode;
      page = 0; // Reset pagination when filtering
      persistensiDataList.clear();
      setLoading(true);
    }

    try {
      String params =
          "agentCode=$agentCode&year=$currentYear&page=$page&size=$limit";

      // Add multiple branch codes filter if selected
      if (selectedBranchCodes.isNotEmpty) {
        for (String branchCode in selectedBranchCodes) {
          params += "&branchCode=$branchCode";
        }
      } else if (selectedBranchCode != null && selectedBranchCode!.isNotEmpty) {
        // Fallback to single branch code for backward compatibility
        params += "&branchCode=$selectedBranchCode";
      }

      // Add search query if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        params += "&q=$searchQuery";
      }

      await api.getPersistencyArea(
        controllers: this,
        code: kReqGetPersistencyArea,
        params: params,
      );
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error fetching data: $e';
      setLoading(false);
      isLoadingMore.value = false;
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Parse pagination info
    parsePagination(response);
    parseData(response);

    setLoading(false);
    isLoadingMore.value = false;
  }

  void parsePagination(response) {
    if (response['totalPages'] != null) {
      maxPage = response['totalPages'] - 1;
    }
    if (response['last'] != null) {
      enableLoadMore = !response['last'];
    }
  }

  void parseData(response) {
    if (response['content'] != null) {
      final List<dynamic> content = response['content'];
      final newData =
          content.map((item) => PersistensiModel.fromJson(item)).toList();

      if (page == 0) {
        persistensiDataList.assignAll(newData);
      } else {
        persistensiDataList.addAll(newData);
      }
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = 'Failed to load data';
    setLoading(false);
    isLoadingMore.value = false;
  }

  String formatPercentage(double value) {
    return "${(value * 100).toStringAsFixed(2)}%";
  }

  void refreshData() {
    page = 0;
    enableLoadMore = true;
    fetchPersistensiData();
  }

  void clearFilter() {
    selectedBranchCode = null;
    refreshData();
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
