import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_area_controller.dart';
import 'package:pdl_superapp/models/user_models.dart';

import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiPageController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team, 2 for Area
  String level = '';
  String agentCode = '';
  String channel = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = true.obs;

  // Agent profile data for other agent view
  Rx<UserModels?> agentProfile = Rx<UserModels?>(null);
  RxBool isLoadingProfile = false.obs;

  // Filter properties
  RxList<String> selectedBranches = <String>[].obs;

  // Search properties
  RxString searchQuery = ''.obs;
  late TextEditingController searchController;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiAreaController areaController = Get.put(
    PersistensiAreaController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiPageController();

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';

    // Initialize search controller
    searchController = TextEditingController();

    // Use agentCode from URL parameter if available, otherwise use stored agentCode
    final urlAgentCode = Get.parameters['agentCode'];
    if (urlAgentCode == null || urlAgentCode.isEmpty) {
      agentCode = prefs.getString(kStorageAgentCode) ?? '';
    }

    // Check if agentCode is provided in URL parameters
    if (urlAgentCode != null && urlAgentCode.isNotEmpty) {
      agentCode = urlAgentCode;
      isShowOtherAgent = true; // Force show other agent data
    }

    // Parse mode from URL parameters for both own and other agent view
    final mode = Get.parameters['mode'];
    if (mode != null && mode.isNotEmpty) {
      final modeIndex = int.tryParse(mode) ?? 0;
      // Validate mode index based on available tabs
      final availableTabsCount = getAvailableTabs().length;
      if (modeIndex >= 0 && modeIndex < availableTabsCount) {
        selectedSection.value = modeIndex;
      }
    } else if (isShowOtherAgent) {
      // For other agent view, always start with index 0 since we only show one tab
      selectedSection.value = 0;
    }

    setLoading(true);

    // Load agent profile if viewing other agent
    if (isShowOtherAgent && agentCode.isNotEmpty) {
      await loadAgentProfile();
      // refreshData() will be called after profile is loaded in loadSuccess()
    } else {
      // For own data, refresh immediately
      refreshData();
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections - robust method
  void switchToSection(int index) {
    final availableTabs = getAvailableTabs();
    if (index >= 0 && index < availableTabs.length) {
      selectedSection.value = index;
      refreshData();
    }
  }

  // Legacy methods for backward compatibility
  void switchToIndividu() {
    // Find index of 'my_self_str' in available tabs
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere((tab) => tab == 'my_self_str'.tr);
    if (index != -1) {
      switchToSection(index);
    }
  }

  void switchToTeam() {
    // Find index of 'team_str' or 'group_str' in available tabs
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere(
      (tab) => tab == 'team_str'.tr || tab == 'group_str'.tr,
    );
    if (index != -1) {
      switchToSection(index);
    }
  }

  void switchToArea() {
    // Find index of 'cabang_str' in available tabs
    final availableTabs = getAvailableTabs();
    final index = availableTabs.indexWhere((tab) => tab == 'cabang_str'.tr);
    if (index != -1) {
      switchToSection(index);
    }
  }

  // Load agent profile for other agent view
  Future<void> loadAgentProfile() async {
    if (!isShowOtherAgent || agentCode.isEmpty) return;

    isLoadingProfile.value = true;
    try {
      await api.getSimpleProfile(
        controllers: this,
        agentCode: agentCode,
        code: kReqGetProfile,
      );
    } catch (e) {
      isLoadingProfile.value = false;
    }
  }

  // Refresh data based on current section and user level
  void refreshData() {
    final query = searchQuery.value.trim();
    final controllerType = getControllerTypeForIndex(selectedSection.value);

    switch (controllerType) {
      case 'individu':
        individuController.fetchPersistensiData(
          searchQuery: query.isEmpty ? null : query,
        );
        break;
      case 'team':
        teamController.fetchPersistensiData(
          searchQuery: query.isEmpty ? null : query,
        );
        break;
      case 'area':
        areaController.fetchPersistensiData(
          branchCodes:
              selectedBranches.isNotEmpty ? selectedBranches.toList() : null,
          searchQuery: query.isEmpty ? null : query,
        );
        break;
    }
    setLoading(false);
  }

  // Search functionality
  void performSearch(String query) {
    searchQuery.value = query;
    refreshData();
  }

  // Clear search
  void clearSearch() {
    searchQuery.value = '';
    refreshData();
  }

  // Get available tabs based on channel and role
  List<String> getAvailableTabs() {
    // If viewing other agent (agentCode parameter), hide all tabs and show only one
    if (isShowOtherAgent) {
      // Get agent level from profile if available
      String? targetLevel;
      if (agentProfile.value != null &&
          agentProfile.value!.agentLevel != null) {
        targetLevel = agentProfile.value!.agentLevel!;
      }

      // If profile not loaded yet, return empty list (will be updated after profile loads)
      if (targetLevel == null) {
        return [];
      }

      // Determine which single tab to show based on target agent level
      if (targetLevel == kLevelBD) {
        return ['group_str'.tr]; // BD shows group
      } else if (targetLevel == kLevelBP) {
        return ['my_self_str'.tr]; // BP shows individu
      } else {
        return ['team_str'.tr]; // BM and others show team
      }
    }

    // Normal logic for own data
    if (channel == kUserChannelBan) {
      // BAN channel - sesuaikan dengan widget controller
      if (level == "BO") {
        return ['my_self_str'.tr];
      } else {
        // ASM and above
        return ['team_str'.tr, 'cabang_str'.tr];
      }
    } else {
      // AGE channel - existing logic
      if (level == kLevelBP) return ['my_self_str'.tr];
      return [
        'my_self_str'.tr,
        level == kLevelBD ? 'group_str'.tr : 'team_str'.tr,
      ];
    }
  }

  // Get the controller type for current tab index
  String getControllerTypeForIndex(int index) {
    final availableTabs = getAvailableTabs();
    if (index >= availableTabs.length) return 'individu';

    final tabName = availableTabs[index];

    if (tabName == 'my_self_str'.tr) {
      return 'individu';
    } else if (tabName == 'team_str'.tr || tabName == 'group_str'.tr) {
      return 'team';
    } else if (tabName == 'cabang_str'.tr) {
      return 'area';
    }

    return 'individu'; // fallback
  }

  // Check if should show tabs (for page view)
  bool shouldShowTabs() {
    // If viewing other agent, never show tabs (always single tab)
    if (isShowOtherAgent) return false;
    return getAvailableTabs().length > 1;
  }

  // Get appropriate column name based on level and controller type
  String getColumnName(String controllerType) {
    if (controllerType == 'team') {
      // Check user level complete for more specific roles
      final userLevelComplete =
          prefs.getString(kStorageUserLevelComplete) ?? '';

      if (level == kLevelASM) {
        return 'nama_bo_str';
      } else if (userLevelComplete == kUserLevelBanRSM) {
        return 'nama_asm_str';
      } else if (userLevelComplete == kUserLevelBanHOS) {
        return 'nama_rsm_str';
      }
    } else if (controllerType == 'area') {
      return 'nama_cabang_str';
    }

    return 'name_str'.tr; // Default "Nama"
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetProfile) {
      if (response != null) {
        agentProfile.value = UserModels.fromJson(response);

        // After profile is loaded, update tabs and refresh data
        if (isShowOtherAgent) {
          // Reset to first tab (index 0) since we only show one tab for other agent
          selectedSection.value = 0;
          // Refresh data with the correct tab
          refreshData();
        }
      }
      isLoadingProfile.value = false;
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    if (requestCode == kReqGetProfile) {
      isLoadingProfile.value = false;
    }
  }

  // Get page title based on whether viewing other agent
  String getPageTitle() {
    return 'persistency_str'.tr;
  }

  // Apply branch filter only
  void applyBranchFilter(List<String> branches) {
    selectedBranches.assignAll(branches);
    // Apply the filter to the current controller if it's area type
    final controllerType = getControllerTypeForIndex(selectedSection.value);
    if (controllerType == 'area') {
      // For area/cabang view, apply branch filter with multiple codes
      final query = searchQuery.value.trim();
      areaController.fetchPersistensiData(
        branchCodes: branches.isNotEmpty ? branches : null,
        searchQuery: query.isEmpty ? null : query,
      );
    }
  }

  // Reset filter
  void resetFilter() {
    selectedBranches.clear();
    // Reset area controller filter
    areaController.fetchPersistensiData();
    refreshData();
  }
}
