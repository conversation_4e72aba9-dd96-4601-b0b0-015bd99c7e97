import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/agent_photo.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/filter_button.dart';
import 'package:pdl_superapp/components/persistensi_filter.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_widget_controller.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

import '../../routes/app_routes.dart';

class PersistensiWidget extends StatelessWidget {
  PersistensiWidget({super.key});

  // Use GetX controller
  final PersistensiWidgetController controller = Get.put(
    PersistensiWidgetController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        spacing: paddingSmall,
        children: [
          // Show tabs based on channel and role (same logic as page)
          if (controller.shouldShowTabs()) _buildTabSelector(context),

          // Show search and filter for team/group view (same logic as page)
          if (controller.shouldShowTabs() &&
              controller.selectedSection.value != 0)
            _buildSearchAndFilter(),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector based on channel and role
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        final availableTabs = controller.getAvailableTabs();

        if (availableTabs.length <= 1) {
          return SizedBox.shrink();
        }
        return Row(
          children:
              availableTabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tabName = entry.value;
                final isSelected = controller.selectedSection.value == index;

                return Expanded(
                  child: InkWell(
                    onTap: () {
                      controller.switchToSection(index);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        border:
                            isSelected
                                ? Border.all(color: kColorBgLight)
                                : null,
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.surface
                                : null,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        tabName,
                        style: TextStyle(
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
      }),
    );
  }

  // Search and filter for BAN channel
  Widget _buildSearchAndFilter() {
    return Row(
      children: [
        Expanded(
          child: PdlTextField(
            textController: controller.searchController,
            hint: 'search_str'.tr,
            prefixIcon: Icon(Icons.search),
            enabled: true,
            textInputAction: TextInputAction.search,
            onChanged: (value) {
              // Optional: implement real-time search
            },
            onEditingComplete: () {
              controller.performSearch(controller.searchController.text);
            },
          ),
        ),
        SizedBox(width: paddingSmall),
        // Only show filter button for area/cabang tab (section 2)
        if (controller.selectedSection.value == 2)
          FilterButton(
            content: PersistensiFilter(
              selectedBranches: controller.selectedBranches,
              onBranchesChanged:
                  (branches) => controller.selectedBranches.assignAll(branches),
              onReset: () => controller.resetFilter(),
              onApply:
                  () =>
                      controller.applyBranchFilter(controller.selectedBranches),
            ),
            title: 'filter_str'.tr,
          ),
      ],
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // Check channel type and route to appropriate content builder
    if (controller.channel == kUserChannelBan) {
      return _buildBanContent();
    } else {
      return _buildAgeContent();
    }
  }

  // BAN Channel Content (existing logic)
  Widget _buildBanContent() {
    return Obx(() {
      final controllerType = controller.getControllerTypeForIndex(
        controller.selectedSection.value,
      );
      switch (controllerType) {
        case 'individu':
          return _buildBanIndividuContent();
        case 'team':
          return _buildBanTeamContent();
        case 'area':
          return _buildBanAreaContent();
        default:
          return _buildBanIndividuContent();
      }
    });
  }

  // AGE Channel Content (new logic)
  Widget _buildAgeContent() {
    // For BP role (AGE channel), show only individu content
    if (controller.level == kLevelBP) {
      return _buildAgeIndividuContent();
    }

    // For other roles, show content based on controller type
    return Obx(() {
      final controllerType = controller.getControllerTypeForIndex(
        controller.selectedSection.value,
      );
      switch (controllerType) {
        case 'individu':
          return _buildAgeIndividuContent();
        case 'team':
          return _buildAgeTeamContent();
        case 'area':
          return _buildAgeAreaContent();
        default:
          return _buildAgeIndividuContent();
      }
    });
  }

  // BAN Individu Content
  Widget _buildBanIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else ...[
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),
              if (controller.level != kLevelBP) SizedBox(height: paddingSmall),
              if (controller.level != kLevelBP)
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    onPressed: () {
                      Get.toNamed('${Routes.PERSISTENSI}?mode=0');
                    },
                    title: "show_details_str".tr,
                  ),
                ),
            ],

            // Refresh Button
            if (!isLoading) ...[
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // BAN Team Content
  Widget _buildBanTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),

            // Refresh Button
            if (!isLoading) ...[
              if (!controller.showFullData.value &&
                  controller.teamController.persistensiDataList.isNotEmpty &&
                  controller.teamController.persistensiDataList.length > 3) ...[
                SizedBox(height: paddingSmall),
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    onPressed: () {
                      Get.toNamed('${Routes.PERSISTENSI}?mode=1');
                    },
                    title: "show_details_str".tr,
                  ),
                ),
                SizedBox(height: paddingSmall),
              ],
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build single table for team/group persistency data
  Widget _buildAgentPersistencyTables() {
    final agents =
        (!controller.showFullData.value &&
                    controller.teamController.persistensiDataList.length > 3
                ? controller.teamController.persistensiDataList.take(3)
                : controller.teamController.persistensiDataList)
            .toList();

    if (agents.isEmpty) {
      return Container();
    }

    // Get appropriate column name for team data
    final columnName = controller.getColumnName('team');

    return _buildClickableTable(
      headers: [columnName.tr, "P-13", "P-25", "P-37", "P-49", "P-61"],
      agents: agents,
      isTeamData: true,
    );
  }

  // BAN Area Content (for Cabang view)
  Widget _buildBanAreaContent() {
    return Obx(() {
      final isLoading = controller.areaController.isLoading.value;
      final hasError = controller.areaController.hasError.value;
      final errorMessage = controller.areaController.errorMessage.value;

      void onRetry() {
        controller.areaController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.areaController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAreaPersistencyTables(),

            // Refresh Button
            if (!isLoading) ...[
              if (!controller.showFullData.value &&
                  controller.areaController.persistensiDataList.isNotEmpty &&
                  controller.areaController.persistensiDataList.length > 3) ...[
                SizedBox(height: paddingSmall),
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    onPressed: () {
                      Get.toNamed('${Routes.PERSISTENSI}?mode=2');
                    },
                    title: "show_details_str".tr,
                  ),
                ),
                SizedBox(height: paddingSmall),
              ],
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Build single table for area persistency data
  Widget _buildAreaPersistencyTables() {
    final areas =
        (!controller.showFullData.value &&
                    controller.areaController.persistensiDataList.length > 3
                ? controller.areaController.persistensiDataList.take(3)
                : controller.areaController.persistensiDataList)
            .toList();

    if (areas.isEmpty) {
      return Container();
    }

    // Get appropriate column name for area data
    final columnName = controller.getColumnName('area');

    return _buildClickableTable(
      headers: [columnName.tr, "P-13", "P-25", "P-37", "P-49", "P-61"],
      agents: areas,
      isTeamData: false,
    );
  }

  // Check if user can click agent names
  bool _canClickAgentNames() {
    // Only allow clicking for team tab (selectedSection == 1)
    if (controller.selectedSection.value != 1) return false;

    // Only allow for specific user levels: RSM, HOS, HOB
    final userLevel =
        controller.prefs.getString(kStorageUserLevelComplete) ?? '';
    return [
      kUserLevelBanRSM,
      kUserLevelBanHOS,
      kUserLevelBanHOB,
    ].contains(userLevel);
  }

  // Build clickable table with name as first column
  Widget _buildClickableTable({
    required List<String> headers,
    required List<PersistensiModel> agents,
    required bool isTeamData,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF0075BD),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(radiusSmall),
                topRight: Radius.circular(radiusSmall),
              ),
            ),
            child: Row(
              children:
                  headers.map((header) {
                    return Expanded(
                      flex:
                          header == headers.first ? 2 : 1, // Name column wider
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          header,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),

          // Data rows
          ...agents.map((agent) {
            if (agent.name == '') return Container();

            return Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  // Name column (conditionally clickable)
                  Expanded(
                    flex: 2,
                    child:
                        _canClickAgentNames()
                            ? InkWell(
                              onTap:
                                  () =>
                                      _navigateToAgentDetail(agent, isTeamData),
                              child: Padding(
                                padding: const EdgeInsets.all(paddingSmall),
                                child: Text(
                                  agent.name,
                                  style: TextStyle(
                                    color: Colors.blue,
                                    decoration: TextDecoration.underline,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              ),
                            )
                            : Padding(
                              padding: const EdgeInsets.all(paddingSmall),
                              child: Text(
                                agent.name,
                                textAlign: TextAlign.left,
                              ),
                            ),
                  ),

                  // Persistency columns
                  if (isTeamData) ...[
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Area data (5 columns)
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // Navigate to agent detail page
  void _navigateToAgentDetail(PersistensiModel agent, bool isTeamData) {
    // Determine mode based on data type
    int mode = isTeamData ? 1 : 2; // 1 for team, 2 for area/cabang
    Get.toNamed(
      '${Routes.PERSISTENSI}?mode=$mode&agentCode=${agent.agentCode}',
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ],
    );
  }

  // ===== AGE CHANNEL CONTENT METHODS =====

  // AGE Individu Content
  Widget _buildAgeIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else ...[
              _buildAgePersistencyData(
                controller.individuController.persistensiData.value!,
              ),
              SizedBox(height: paddingSmall),
              SizedBox(
                width: double.infinity,
                child: PdlButton(
                  onPressed: () {
                    Get.toNamed('${Routes.PERSISTENSI}?mode=0');
                  },
                  title: "show_details_str".tr,
                ),
              ),
            ],

            // Refresh Button
            if (!isLoading) ...[
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // AGE Team Content
  Widget _buildAgeTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgeTeamPersistencyList(),

            // Refresh Button
            if (!isLoading) ...[
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // AGE Area Content (placeholder for now)
  Widget _buildAgeAreaContent() {
    return _buildAgeTeamContent(); // Use same as team for now
  }

  // Build AGE persistency data display (individual format)
  Widget _buildAgePersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build AGE team persistency list (table format with horizontal scroll)
  Widget _buildAgeTeamPersistencyList() {
    final agents =
        (!controller.showFullData.value &&
                    controller.teamController.persistensiDataList.length > 3
                ? controller.teamController.persistensiDataList.take(3)
                : controller.teamController.persistensiDataList)
            .toList();

    if (agents.isEmpty) {
      return Container();
    }

    return Column(
      children: [
        _buildAgeScrollableTable(agents),
        SizedBox(height: paddingMedium),
        SizedBox(
          width: double.infinity,
          child: PdlButton(
            onPressed: () {
              Get.toNamed('${Routes.PERSISTENSI}?mode=1');
            },
            title: "show_details_str".tr,
          ),
        ),
      ],
    );
  }

  // Build AGE scrollable table with horizontal scroll
  Widget _buildAgeScrollableTable(List<PersistensiModel> agents) {
    return Column(
      spacing: paddingMedium,
      children:
          agents.map((agent) {
            if (agent.name == '') return Container();

            return _buildAgeAgentCard(agent);
          }).toList(),
    );
  }

  // Build individual AGE agent card with table styling
  Widget _buildAgeAgentCard(PersistensiModel agent) {
    return Column(
      spacing: paddingSmall,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header with profile picture, level and name
        Row(
          children: [
            AgentPhotoFactory.small(
              photoUrl: agent.agentPhoto.isNotEmpty ? agent.agentPhoto : null,
              agentName: agent.name,
            ),
            SizedBox(width: paddingSmall),
            Expanded(
              child: Text(
                '${agent.agentLevel} ${agent.name}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),

        // Persistency data table
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Container(
            decoration: BoxDecoration(
              color: Get.isDarkMode ? kColorGlobalBgDarkBlue : kLine,
              borderRadius: BorderRadius.all(Radius.circular(radiusSmall)),
            ),
            child: Column(
              children: [
                // Headers row (blue background)
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xFF0075BD),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(radiusSmall),
                      topRight: Radius.circular(radiusSmall),
                    ),
                  ),
                  child: Row(
                    children:
                        ['P-13', 'P-25', 'P-37', 'P-49', 'P-61'].map((header) {
                          return Container(
                            width: 80,
                            padding: const EdgeInsets.symmetric(
                              vertical: paddingSmall,
                            ),
                            child: Text(
                              header,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                color: Colors.white,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ),

                // Values row (transparent background)
                Container(
                  margin: EdgeInsets.symmetric(vertical: paddingSmall),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 80,
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 80,
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 80,
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 80,
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 80,
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
