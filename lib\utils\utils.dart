import 'dart:async';
import 'dart:developer' as dev;
import 'dart:io';
import 'dart:math';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/import_helper_web/empty_stub.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/languages/languages.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/secure_storage_service.dart';
import 'package:pdl_superapp/utils/theme_utils.dart';
import 'package:pdl_superapp/utils/themes/theme_primary.dart';
import 'package:restart_app/restart_app.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import '../components/pdl_bottom_sheet.dart';
import '../controllers/network_controller.dart';
import '../routes/app_routes.dart';
import 'svg_helper/svg_helper.dart';

enum ControllerState {
  firstLoad,
  loading,
  loadingSuccess,
  loadingFailed,
  reload,
}

class Utils {
  static Widget cachedImageWrapper(
    url, {
    double? width,
    Color? color,
    BoxFit? fit,
    bool? isFullUrl,
    Alignment? alignment,
  }) {
    // First try to get the asset URL from the theme service
    String themeKey = 'default';
    try {
      // Check if the theme is loaded and if the asset exists in the theme
      if (ThemeUtils.isThemeLoaded) {
        // Get the theme key
        themeKey = ThemeUtils.currentTheme.key;

        // Print the theme key
        _log('Theme key for image: $themeKey');
      }
    } catch (e) {
      _logError('Error getting theme asset URL: $e');
    }
    if (url == null || url.toString().trim().isEmpty) {
      _logError('cachedImageWrapper called with null or empty URL: $url');
      return const SizedBox.shrink();
    }
    String cleanUrl = url.replaceAll('image/', '');
    cleanUrl = cleanUrl.replaceAll('icon/', '');

    // Check if cleanUrl is empty after processing
    if (cleanUrl.trim().isEmpty) {
      _logError(
        'cachedImageWrapper: cleanUrl is empty after processing URL: $url',
      );
      return const SizedBox.shrink();
    }

    String baseUrl =
        'https://pdl-superapp-uat-s3.s3.ap-southeast-3.amazonaws.com/themes';
    String activeTheme = themeKey.isNotEmpty ? themeKey : 'default';
    // For web platform, use smart fallback hierarchy
    if (kIsWeb) {
      return _buildWebImageFallback(
        url,
        cleanUrl,
        baseUrl,
        activeTheme,
        width,
        color,
        fit,
        alignment,
        isFullUrl,
      );
    }

    // For mobile platforms, use original approach
    return CachedNetworkImage(
      imageUrl: isFullUrl == true ? url : '$baseUrl/$activeTheme/$cleanUrl',
      width: width,
      // ignore: deprecated_member_use
      color: color,
      fit: fit ?? BoxFit.contain,
      alignment: alignment ?? Alignment.center,
      errorWidget: (context, imageUrl, error) {
        _logError('Failed to load network image: $imageUrl, error: $error');
        // Fallback to local asset
        try {
          return Image.asset(
            'assets/$cleanUrl',
            width: width,
            color: color,
            fit: fit ?? BoxFit.contain,
            alignment: alignment ?? Alignment.center,
            errorBuilder: (context, error, stackTrace) {
              _logError(
                'Failed to load asset image: assets/$cleanUrl, error: $error',
              );
              // Final fallback to icon
              return Container(
                width: width,
                color: Colors.grey[300],
                child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
              );
            },
          );
        } catch (e) {
          _logError('Error creating asset image widget: $e');
          // Final fallback to icon
          return Container(
            width: width,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        }
      },
    );
  }

  static Widget cachedSvgWrapper(
    url, {
    double? width,
    double? height,
    Color? color,
    BoxFit? fit,
  }) {
    if (url == null || url.toString().trim().isEmpty) {
      _logError('cachedSvgWrapper called with null or empty URL: $url');
      return const SizedBox.shrink();
    }

    // First try to get the asset URL from the theme service
    String themeKey = 'default';

    try {
      // Check if the theme is loaded and if the asset exists in the theme
      if (ThemeUtils.isThemeLoaded) {
        // Get the theme key
        themeKey = ThemeUtils.currentTheme.key;

        // Print the theme key
        _log('Theme key for SVG: $themeKey');
      }
    } catch (e) {
      _logError('Error getting theme asset URL for SVG: $e');
    }

    // Fallback to the default URL if theme asset not found

    String cleanUrl = url.replaceAll('image/', '');
    cleanUrl = cleanUrl.replaceAll('icon/', '');

    // Check if cleanUrl is empty after processing
    if (cleanUrl.trim().isEmpty) {
      _logError(
        'cachedSvgWrapper: cleanUrl is empty after processing URL: $url',
      );
      return const SizedBox.shrink();
    }

    String baseUrl =
        'https://pdl-superapp-uat-s3.s3.ap-southeast-3.amazonaws.com/themes';
    String activeTheme = themeKey.isNotEmpty ? themeKey : 'default';

    // For web platform, use smart fallback hierarchy
    if (kIsWeb) {
      return _buildWebSvgFallback(
        url,
        cleanUrl,
        baseUrl,
        activeTheme,
        width,
        height,
        color,
        fit,
      );
    }
    // For non-web platforms, use CachedNetworkSVGImage
    return _buildCachedNetworkSvg(
      '$baseUrl/$activeTheme/$cleanUrl',
      width: width,
      height: height,
      color: color,
      fit: fit ?? BoxFit.contain,
      errorWidget: _buildSvgFallback(url, width, height, color, fit),
    );
  }

  /// Helper method to build platform-specific cached network SVG
  static Widget _buildCachedNetworkSvg(
    String url, {
    double? width,
    double? height,
    Color? color,
    BoxFit? fit,
    Widget? errorWidget,
  }) {
    // Use platform-specific SVG helper
    return buildCachedNetworkSvg(
      url,
      width: width,
      height: height,
      color: color,
      fit: fit,
      errorWidget: errorWidget,
    );
  }

  /// Helper method to build SVG fallback widget
  static Widget _buildSvgFallback(
    String url,
    double? width,
    double? height,
    Color? color,
    BoxFit? fit,
  ) {
    _logError('Failed to load network SVG, attempting fallback to assets/$url');

    // Check if URL is empty to prevent loading "assets/"
    if (url.trim().isEmpty) {
      _logError(
        '_buildSvgFallback called with empty URL, returning fallback icon',
      );
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
      );
    }

    // In release mode, use a simpler fallback to avoid type casting issues
    if (kReleaseMode && kIsWeb) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey[600],
          size:
              (width != null && height != null)
                  ? (width < height ? width : height) * 0.5
                  : 24,
        ),
      );
    }

    // Fallback to local asset (debug mode or non-web)
    try {
      return SvgPicture.asset(
        'assets/$url',
        width: width,
        height: height,
        colorFilter:
            color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
        fit: fit ?? BoxFit.contain,
        placeholderBuilder: (context) {
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: const CircularProgressIndicator(),
          );
        },
        // Add error builder for asset SVG as well
        // ignore: deprecated_member_use
        errorBuilder: (context, error, stackTrace) {
          _logError('Asset SVG also failed: assets/$url, error: $error');
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        },
      );
    } catch (e) {
      _logError('Error creating asset SVG widget: $e');
      // Final fallback to icon
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
      );
    }
  }

  static getStatusTr(String status) {
    switch (status) {
      case 'DRAFT':
        return 'Draft';
      case 'Semua':
        return 'trx_stat_all_str'.tr;
      case 'IN_PROGRESS':
        return 'trx_stat_in_progress_str'.tr;
      case 'COMPLETE':
        return 'trx_stat_completed_str'.tr;
      case 'REJECTED':
        return 'trx_stat_rejected_str'.tr;
      case 'CANCELLED':
        return 'trx_stat_cancelled_str'.tr;
      case 'DIKEMBALIKAN':
        return 'trx_stat_returned_str'.tr;
      case 'EXPIRED':
        return 'trx_stat_expired_str'.tr;
      default:
        return status;
    }
  }

  static setLoggedIn({
    required String token,
    required String refreshToken,
    required String username,
    required String password,
    required String languageCode,
    required bool needPassReset,
    required int remainingDays,
  }) async {
    // print('here $remainingDays');
    // print('here $needPassReset');
    // return;
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(kStorageToken, token);
    await prefs.setString(kStorageRefreshToken, refreshToken);

    // save encrypted username and password to local devices for Fingerprint
    await prefs.setString(kStorageUsername, username);
    await prefs.setString(kStoragePassword, password);

    // pass reset condition
    await prefs.setBool(kStorageNeedChangePass, false);

    // if(remainingDays <= 7) <- update password but, there is skip button
    if (remainingDays > 0 && remainingDays <= 7 && needPassReset) {
      await prefs.setBool(kStorageNeedChangePass, false);
      Get.offAllNamed(
        Routes.FIRST_LOGIN_SUCCESS,
        parameters: {
          'remainingDays': remainingDays.toString(),
          'languageCode': languageCode,
        },
      );

      return;
    }

    // if(needPassReset == true) <- must update password
    if (needPassReset) {
      await prefs.setBool(kStorageNeedChangePass, needPassReset);
      Get.offAllNamed(Routes.FIRST_LOGIN_SUCCESS);
      return;
    }
    Get.offAllNamed(Routes.MAIN, arguments: {'language': languageCode});
  }

  static setLoggedOut({bool? noRedirect}) async {
    if (Get.currentRoute == Routes.LOGIN) {
      return;
    }
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(kStorageToken);
    prefs.remove(kStorageRefreshToken);
    prefs.remove(kStorageAgentCode);
    prefs.remove(kStorageAgentName);
    prefs.remove(kStorageUserId);
    prefs.remove(kStorageUserFirestoreId);

    // Clear secure storage credentials when logging out
    await SecureStorageService.clearCredentials();

    if (noRedirect == true) {
      return;
    }
    Get.offAllNamed(Routes.LOGIN);
  }

  /// Smart back navigation that handles both history-based and manual navigation
  /// If no history exists, navigates to home page
  static void smartBack() {
    // Try to go back using history first
    if (Get.context != null && Navigator.canPop(Get.context!)) {
      Get.back();
    } else {
      // If no history, manually navigate to parent route
      String currentRoute = Get.currentRoute;
      String parentRoute = _getParentRoute(currentRoute);
      if (parentRoute.isNotEmpty) {
        Get.offNamed(parentRoute);
      } else {
        // Fallback to home if no parent route can be determined
        Get.offNamed(Routes.MAIN);
      }
    }
  }

  /// Extract parent route by removing the last path segment
  static String _getParentRoute(String currentRoute) {
    if (currentRoute.isEmpty || currentRoute == '/') return '';

    // Remove query parameters if any
    String route = currentRoute.split('?').first;

    // Split by '/' and remove empty strings
    List<String> segments =
        route.split('/').where((s) => s.isNotEmpty).toList();

    if (segments.isEmpty) return '';

    // Remove the last segment (which could be an ID or detail page)
    segments.removeLast();

    if (segments.isEmpty) return Routes.MAIN;

    return '/${segments.join('/')}';
  }

  static setBiometric() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isActive = prefs.getBool(kStorageIsBiometricActive) ?? false;

    await prefs.setBool(kStorageIsBiometricActive, !isActive);
  }

  static Future<String> getFullVersionApp() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String result = '(${packageInfo.buildNumber})';

    try {
      final updater = ShorebirdUpdater();
      // appVersion.value = packageInfo.version;
      // buildNumber.value = packageInfo.buildNumber;
      final currentPatch = await updater.readCurrentPatch();
      int patchNumber = currentPatch?.number ?? 0;
      SnackBar(content: Text('Patch Number $patchNumber'));
      if (patchNumber != 0) {
        result =
            '${packageInfo.version}+${packageInfo.buildNumber}#$patchNumber';
      } else {
        result = '${packageInfo.version}+${packageInfo.buildNumber}';
      }
    } catch (error) {
      // If there's an error reading patch info, just use the basic version
      result = '${packageInfo.version}+${packageInfo.buildNumber}';
    }

    return result;
  }

  static Future<void> checkForUpdates() async {
    final updater = ShorebirdUpdater();

    try {
      // Check whether a new update is available
      final status = await updater.checkForUpdate();

      if (status == UpdateStatus.outdated) {
        // Show confirmation dialog before updating
        final shouldUpdate = await Get.dialog<bool>(
          AlertDialog(
            title: const Text('Pembaharuan Tersedia'),
            content: const Text(
              'Terdapat pembaharuan aplikasi yang tersedia. Apakah Anda ingin mengunduh dan menginstal pembaharuan sekarang?',
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('Nanti'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                child: const Text('Update'),
              ),
            ],
          ),
          barrierDismissible: false,
        );

        // If user confirmed the update
        if (shouldUpdate == true) {
          // Show loading dialog
          Get.dialog(
            Material(
              color: Colors.transparent,
              child: SizedBox(
                width: Get.width,
                height: Get.height,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(paddingMedium),
                    decoration: BoxDecoration(
                      color: kColorBgLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'Mengunduh pembaharuan...',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: paddingSmall),
                        const CircularProgressIndicator(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            barrierDismissible: false,
          );

          try {
            // Perform the update
            await updater.update();

            // Close loading dialog
            Get.back();

            // Show restart confirmation dialog with countdown
            await _showRestartDialog();
          } on UpdateException catch (error) {
            // Handle update errors
            Get.back(); // Close loading dialog
            Get.snackbar(
              'Update Failed',
              'Failed to download update: ${error.message}',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: kColorGlobalBgRed,
              colorText: kColorErrorText,
            );
          }
        }
      }
    } catch (error) {
      // Handle general errors (network issues, etc.)
      Get.snackbar(
        'Error',
        'Gagal memeriksa pembaharuan: $error',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
  }

  static Future<void> _showRestartDialog() async {
    final RxInt countdown = 5.obs;

    // Show initial dialog
    Get.dialog(
      AlertDialog(
        title: const Text('Pembaharuan Berhasil'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Pembaharuan berhasil diunduh. Aplikasi akan ditutup dalam:',
            ),
            const SizedBox(height: paddingSmall),
            Obx(
              () => Text(
                '${countdown.value} detik',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
            const SizedBox(height: paddingSmall),
            const Text(
              'Silakan buka kembali aplikasi untuk menggunakan versi terbaru.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () async {
              Get.back();
              await _closeApp();
            },
            child: const Text('Tutup Sekarang'),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    // Start countdown
    Timer.periodic(const Duration(seconds: 1), (timer) {
      countdown.value--;
      if (countdown.value <= 0) {
        timer.cancel();
        Get.back(); // Close dialog
        _closeApp();
      }
    });
  }

  static Future<void> _closeApp() async {
    try {
      if (isAndroid) {
        // For Android, force close the app completely
        // First try SystemNavigator.pop()
        SystemNavigator.pop();
        // Then force exit to ensure complete closure
        Future.delayed(const Duration(milliseconds: 100), () {
          exit(0);
        });
      } else if (isIOS) {
        // For iOS, we can't force close the app due to App Store guidelines
        // So we'll use restart_app as fallback
        // Add flag to indicate app was restarted due to Shorebird update
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('shorebird_restart_flag', true);

        Restart.restartApp(
          notificationTitle: 'Apps Berhasil di update',
          notificationBody:
              'Silakan buka kembali aplikasi untuk menggunakan versi terbaru.',
        );
      } else {
        // For other platforms, try restart_app
        Restart.restartApp();
      }
    } catch (e) {
      // Fallback to restart_app if other methods fail
      Restart.restartApp();
    }
  }

  static Future<String> getBuildNumber() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String result = '(${packageInfo.buildNumber})';
    return result;
  }

  static Future<String> getAppVersionOnly() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String result = '(${packageInfo.version})';
    return result;
  }

  static int getMonthDifference(DateTime date) {
    DateTime today = DateTime.now();
    int yearDiff = today.year - date.year;
    int monthDiff = today.month - date.month;

    return (yearDiff * 12) + monthDiff;
  }

  static Future<String> getDeviceId() async {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String deviceId = '';
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;
      deviceId = '${webBrowserInfo.appCodeName}';
    } else if (isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;
      deviceId = androidDeviceInfo.id;
    } else if (isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfoPlugin.iosInfo;
      deviceId = '${iosDeviceInfo.identifierForVendor}';
    }
    return deviceId;
  }

  static String getInitials(String name) {
    List<String> words = name.trim().split(' ');
    String initials =
        words
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
            .join();
    return initials.length > 2 ? initials.substring(0, 2) : initials;
  }

  static imagePicker(
    context, {
    bool? isCamera,
    required Function(XFile) onSuccess,
    Function(String)? onError,
    bool validateFileSize = false,
    double maxSizeMB = 2.0,
  }) async {
    final ImagePicker picker = ImagePicker();

    final XFile? image = await picker.pickImage(
      source: isCamera == true ? ImageSource.camera : ImageSource.gallery,
    );

    if (image != null) {
      // Validate file size if requested
      if (validateFileSize) {
        try {
          int fileSizeInBytes = await image.length();
          double fileSizeInMB = fileSizeInBytes / (1024 * 1024);

          if (fileSizeInMB >= maxSizeMB) {
            if (onError != null) {
              onError('photo_max_2mb'.tr);
            } else {
              Get.snackbar('Error', 'photo_max_2mb'.tr);
            }
            return;
          }
        } catch (e) {
          debugPrint('Error checking file size: $e');
          // Continue with upload if size check fails
        }
      }

      onSuccess(image);
    }
  }

  static getFileSize(String filepath, int decimals) {
    if (kIsWeb) {
      // On web, File.lengthSync() is not supported
      // Return a placeholder or try alternative methods
      return "Unknown size";
    }

    var file = File(filepath);
    int bytes = file.lengthSync();
    if (bytes <= 0) return "0 B";
    const suffixes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    var i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  static bool setTheme(bool isDarkTheme) {
    bool result = false;

    if (isDarkTheme) {
      Get.changeTheme(ThemePrimary.dark);
      result = true;
    } else {
      Get.changeTheme(ThemePrimary.light);
      result = false;
    }

    return result;
  }

  static setLocale(String locale) {
    if (locale == 'id_ID') {
      Languages().changeLocale('Indonesia');
    } else {
      try {
        Languages().changeLocale('English');
      } catch (e) {
        // print('here $e');
      }
    }
  }

  static String currencyFormatters({String? data, String? currency}) {
    final currencyFormatter = NumberFormat('#,##0', 'ID');

    if (data == 'null') {
      data = null;
    }
    String stringNumber = currencyFormatter.format(double.parse(data ?? '0.0'));

    return '${currency ?? 'Rp'}$stringNumber';
  }

  static String getRandomString({int length = 8}) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final rand = Random();
    return List.generate(
      length,
      (_) => chars[rand.nextInt(chars.length)],
    ).join('');
  }

  /// Log message using LoggerService if available
  static void _log(String message) {
    try {
      Get.find<LoggerService>().log('[Utils] $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[Utils] $message');
    }
  }

  /// Log error using LoggerService if available
  static void _logError(String message) {
    try {
      Get.find<LoggerService>().log('[Utils] ERROR: $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[Utils] ERROR: $message');
    }
  }

  static ThemeData dropDownThemeData(context) {
    final dropdownTheme = Theme.of(context).copyWith(
      inputDecorationTheme: const InputDecorationTheme(
        errorBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
      ),
    );
    return dropdownTheme;
  }

  static CustomDropdownDecoration dropdownDecoration(context) {
    final dropdownDecoration = CustomDropdownDecoration(
      closedBorderRadius: BorderRadius.circular(8),
      expandedBorderRadius: BorderRadius.circular(8),
      closedFillColor: Theme.of(context).colorScheme.surface,
      expandedFillColor: Theme.of(context).colorScheme.surface,
      closedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      expandedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      listItemStyle: Theme.of(context).textTheme.bodyMedium,
      hintStyle: Theme.of(context).textTheme.bodyMedium,
      headerStyle: Theme.of(context).textTheme.bodyMedium,
    );
    return dropdownDecoration;
  }

  static Future<CroppedFile?> cropKtpImage(
    BuildContext context, {
    required String imagePath,
    String title = 'Sesuaikan Ukuran KTP',
  }) async {
    final croppedImage = await ImageCropper().cropImage(
      sourcePath: imagePath,
      aspectRatio: CropAspectRatio(ratioX: ktpAspectRatio, ratioY: 1),
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: title,
          toolbarColor: kColorGlobalBlue,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
          hideBottomControls: false,
        ),
        IOSUiSettings(
          title: title,
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
          aspectRatioPickerButtonHidden: true,
        ),
        WebUiSettings(
          context: context,
          presentStyle: WebPresentStyle.dialog,
          size: CropperSize(
            width: (Get.width - paddingLarge).toInt(),
            height: (Get.height / 1.7).toInt(),
          ),
        ),
      ],
    );
    return croppedImage;
  }

  static DottedBorder customDottedBorder({required Widget child}) {
    return DottedBorder(
      radius: Radius.circular(8),
      borderType: BorderType.RRect,
      color: Get.isDarkMode ? kColorBorderDark : kColorTextDark,
      dashPattern: [8, 8],
      child: child,
    );
  }

  /// Menampilkan popup notifikasi
  static void popup({required String body, required String type}) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (type) {
      case kPopupSuccess:
        backgroundColor = kColorGlobalBgGreen;
        textColor = kColorGlobalGreen;
        icon = Icons.check_circle;
        break;
      case kPopupFailed:
        backgroundColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        icon = Icons.error;
        break;
      case kPopupWarning:
        backgroundColor = kColorGlobalBgWarning;
        textColor = kColorGlobalWarning;
        icon = Icons.warning;
        break;
      case kPopupInfo:
      default:
        backgroundColor = kColorGlobalBgBlue;
        textColor = kColorGlobalBlue;
        icon = Icons.info;
        break;
    }

    Get.snackbar(
      '',
      '',
      backgroundColor: backgroundColor,
      borderRadius: 8,
      margin: EdgeInsets.all(paddingMedium),
      padding: EdgeInsets.symmetric(
        horizontal: paddingMedium,
        vertical: paddingSmall,
      ),
      titleText: Container(),
      messageText: Row(
        children: [
          Icon(icon, color: textColor),
          SizedBox(width: paddingSmall),
          Expanded(child: Text(body, style: TextStyle(color: textColor))),
        ],
      ),
      duration: Duration(seconds: 3),
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Menampilkan dialog box yang auto close setelah 2 detik dan tidak bisa di-close manual
  static void autoCloseDialog({required String body, required String type}) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (type) {
      case kPopupSuccess:
        backgroundColor = kColorGlobalBgGreen;
        textColor = kColorGlobalGreen;
        icon = Icons.check_circle;
        break;
      case kPopupFailed:
        backgroundColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        icon = Icons.error;
        break;
      case kPopupWarning:
        backgroundColor = kColorGlobalBgWarning;
        textColor = kColorGlobalWarning;
        icon = Icons.warning;
        break;
      case kPopupInfo:
      default:
        backgroundColor = kColorGlobalBgBlue;
        textColor = kColorGlobalBlue;
        icon = Icons.info;
        break;
    }

    Get.dialog(
      Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            margin: EdgeInsets.all(paddingMedium),
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: textColor),
                SizedBox(width: paddingSmall),
                Flexible(
                  child: Text(
                    body,
                    style: TextStyle(color: textColor),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false, // Tidak bisa di-close dengan tap di luar
    );

    // Auto close setelah 3 detik
    Timer(Duration(seconds: 3), () {
      if (Get.isDialogOpen == true) {
        Get.back();
      }
    });
  }

  static Widget getTrxStatus(context, String trxStatus) {
    Widget result = Container();
    Color statusColor = kColorGlobalBlue;
    Color textColor = kColorGlobalBlue;
    String statusText = trxStatus;
    switch (trxStatus.toUpperCase()) {
      // DRAFT, IN_PROGRESS, COMPLETE, EXPIRED, REJECTED, CANCELLED, DIKEMBALIKAN
      case 'DRAFT':
        statusColor =
            Get.isDarkMode ? kColorGlobalBgDarkBlue : kColorBorderLight;
        textColor = Get.isDarkMode ? kColorTextDark : kColorTextLight;
        statusText = getStatusTr(trxStatus);
        break;
      case 'IN_PROGRESS':
        statusColor = kColorGlobalBgBlue;
        textColor = kColorGlobalBlue;
        statusText = getStatusTr(trxStatus);
        break;
      case 'COMPLETE':
        statusColor = kColorGlobalBgGreen;
        textColor = kColorGlobalGreen;
        statusText = getStatusTr(trxStatus);
        break;
      case 'REJECTED':
        statusColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        statusText = getStatusTr(trxStatus);
        break;
      case 'CANCELLED':
        statusColor = kColorGlobalBgRed;
        textColor = kColorGlobalRed;
        statusText = getStatusTr(trxStatus);
        break;
      case 'DIKEMBALIKAN':
        statusColor = kColorGlobalBgWarning;
        textColor = kColorGlobalWarning;
        statusText = getStatusTr(trxStatus);
      case 'EXPIRED':
        statusColor =
            Get.isDarkMode ? kColorGlobalBgDarkBlue : kColorBorderLight;
        textColor = Get.isDarkMode ? kColorTextDark : kColorTextLight;
        statusText = getStatusTr(trxStatus);
        break;
      default:
        statusColor = Colors.transparent;
        statusText = 'Unknown';
    }
    result = Container(
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(paddingExtraSmall),
      child: Text(
        statusText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
    return result;
  }

  static Widget getApprovalStatus(context, String approvalStatus) {
    Widget result = Container();
    // BARU, TERTUNDA, MENUNGGU_PERSETUJUAN, DISETUJUI, DITOLAK, DIBATALKAN
    Color statusColor = Get.isDarkMode ? kColorBorderDark : kColorBorderLight;
    Color statusTextColor = Get.isDarkMode ? kColorTextDark : kColorTextLight;

    switch (approvalStatus) {
      case 'BARU':
        statusColor = kColorGlobalBgBlue;
        statusTextColor = kColorGlobalBlue;
        break;
      case 'DIKEMBALIKAN':
        statusColor = kColorGlobalBgWarning;
        statusTextColor = kColorGlobalWarning;
        break;
      case 'MENUNGGU_PERSETUJUAN':
        statusColor = kColorGlobalBgBlue;
        statusTextColor = kColorGlobalBlue;
        break;
      case 'DISETUJUI':
        statusColor = kColorGlobalBgGreen;
        statusTextColor = kColorGlobalGreen;
        break;
      case 'DITOLAK':
        statusColor = kColorGlobalBgRed;
        statusTextColor = kColorGlobalRed;
        break;
      case 'DIBATALKAN':
        statusColor = kColorGlobalBgRed;
        statusTextColor = kColorGlobalRed;
        break;
      default:
    }
    result = Container(
      margin: EdgeInsets.only(top: paddingSmall),
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(radiusSmall),
      ),
      padding: EdgeInsets.all(paddingExtraSmall),
      child: Text(
        approvalStatus.replaceAll('_', ' '),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: statusTextColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
    return result;
  }

  static String formatDate(param0, {required String format}) {
    try {
      final date = DateTime.parse(param0).toLocal();
      return DateFormat(format).format(date);
    } catch (e) {
      return param0;
    }
  }

  static String formatCustomDate(String dateStr) {
    if (dateStr.isEmpty) return '';

    try {
      final date = DateTime.parse(dateStr).toLocal();
      final now = DateTime.now();

      final difference = now.difference(date);

      // Kurang dari 8 jam
      if (difference.inHours < 8) {
        int hours = difference.inHours;
        int minutes = difference.inMinutes % 60;

        if (hours > 0) {
          return '$hours jam $minutes menit yang lalu';
        } else {
          return '$minutes menit yang lalu';
        }
      }

      // Masih hari ini
      if (date.day == now.day &&
          date.month == now.month &&
          date.year == now.year) {
        return DateFormat('HH:mm').format(date);
      }

      // Selain itu
      return DateFormat('dd MMM yyyy HH:mm').format(date);
    } catch (e) {
      return dateStr; // fallback jika format gagal
    }
  }

  void showNoConnectionDialog(
    // BuildContext context,
    NetworkController networkController,
  ) {
    // Cek apakah dialog sudah pernah ditampilkan dalam sesi ini
    if (Get.isBottomSheetOpen ?? false) return;
    if (networkController.hasShownDisconnectedDialog) return;

    String title = 'title_disconected_str'.tr;
    String subTitle = 'subtitle_internet_disconnected_str'.tr;

    // Tandai bahwa dialog sudah ditampilkan
    networkController.markDisconnectedDialogShown();

    PdlBottomSheet(
      title: '',
      content: Padding(
        padding: EdgeInsets.only(bottom: paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Utils.cachedSvgWrapper('icon/illustration-empty-no-connection.svg'),
            Text(
              title,
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            Text(subTitle, style: TextStyle(color: kColorTextTersierLight)),
            SizedBox(
              width: Get.width * .9,
              child: Container(
                padding: EdgeInsets.only(top: paddingLarge),
                width: Get.width,
                child: FilledButton(
                  onPressed: () {
                    kIsWeb ? null : networkController.retry();
                  },
                  child: Text('retry_again_str'.tr),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Web-specific image fallback with hierarchy: active theme → default theme → local assets
  static Widget _buildWebImageFallback(
    String originalUrl,
    String cleanUrl,
    String baseUrl,
    String activeTheme,
    double? width,
    Color? color,
    BoxFit? fit,
    Alignment? alignment,
    bool? isFullUrl,
  ) {
    return FutureBuilder<Widget>(
      future: _tryImageFallbackHierarchy(
        originalUrl,
        cleanUrl,
        baseUrl,
        activeTheme,
        width,
        color,
        fit,
        alignment,
        isFullUrl,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done ||
            snapshot.hasError ||
            snapshot.data == null ||
            snapshot.data is! Widget) {
          _logError('Image FutureBuilder error: ${snapshot.error}');
          return _buildFinalImageFallback(originalUrl, width, color);
        }

        return snapshot.data!;
      },
    );
  }

  /// Try image loading with fallback hierarchy for web
  static Future<Widget> _tryImageFallbackHierarchy(
    String originalUrl,
    String cleanUrl,
    String baseUrl,
    String activeTheme,
    double? width,
    Color? color,
    BoxFit? fit,
    Alignment? alignment,
    bool? isFullUrl,
  ) async {
    // Additional safety check for empty URLs
    if (originalUrl.trim().isEmpty || cleanUrl.trim().isEmpty) {
      _logError(
        '_tryImageFallbackHierarchy called with empty URL: originalUrl=$originalUrl, cleanUrl=$cleanUrl',
      );
      return Future.value(_buildFinalImageFallback(originalUrl, width, color));
    }
    Widget buildImage(String url) {
      return CachedNetworkImage(
        imageUrl: url,
        width: width,
        color: color,
        fit: fit ?? BoxFit.contain,
        alignment: alignment ?? Alignment.center,
        errorWidget: (context, imageUrl, error) {
          _logError('Failed to load image: $imageUrl | $error');
          return _buildFinalImageFallback(originalUrl, width, color);
        },
      );
    }

    // Step 1: Try active theme (if not default)
    if (activeTheme != 'default') {
      final activeThemeUrl =
          isFullUrl == true ? originalUrl : '$baseUrl/$activeTheme/$cleanUrl';
      try {
        final response = await http.head(Uri.parse(activeThemeUrl));
        if (response.statusCode == 200) {
          _log('Loading image from active theme: $activeTheme');
          return Future.value(buildImage(activeThemeUrl));
        }
      } catch (e) {
        _logError('Active theme image check failed: $e');
      }
    }

    // Step 2: Try default theme
    final defaultThemeUrl =
        isFullUrl == true ? originalUrl : '$baseUrl/default/$cleanUrl';
    try {
      final response = await http.head(Uri.parse(defaultThemeUrl));
      if (response.statusCode == 200) {
        _log('Loading image from default theme');
        return Future.value(buildImage(defaultThemeUrl));
      }
    } catch (e) {
      _logError('Default theme image check failed: $e');
    }

    // Step 3: Try local asset
    try {
      _log('Attempting to load image from asset: assets/$originalUrl');
      return Future.value(
        Image.asset(
          'assets/$originalUrl',
          width: width,
          color: color,
          fit: fit ?? BoxFit.contain,
          alignment: alignment ?? Alignment.center,
          errorBuilder: (context, error, stackTrace) {
            _logError('Asset image failed: $error');
            return _buildFinalImageFallback(originalUrl, width, color);
          },
        ),
      );
    } catch (e) {
      _logError('Exception loading asset image: $e');
      return Future.value(_buildFinalImageFallback(originalUrl, width, color));
    }
  }

  /// Final fallback for images
  static Widget _buildFinalImageFallback(
    String url,
    double? width,
    Color? color,
  ) {
    try {
      _logError('Using final image fallback for: $url');
      return Container(
        width: width,
        color: Colors.grey[300],
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey[600],
          size: width != null ? width * 0.3 : 48,
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  /// Web-specific SVG fallback with hierarchy: active theme → default theme → local assets
  static Widget _buildWebSvgFallback(
    String originalUrl,
    String cleanUrl,
    String baseUrl,
    String activeTheme,
    double? width,
    double? height,
    Color? color,
    BoxFit? fit,
  ) {
    return FutureBuilder<Widget>(
      future: _trySvgFallbackHierarchy(
        originalUrl,
        cleanUrl,
        baseUrl,
        activeTheme,
        width,
        height,
        color,
        fit,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done ||
            snapshot.hasError ||
            snapshot.data == null ||
            snapshot.data is! Widget) {
          _logError('SVG fallback error or invalid widget: ${snapshot.error}');
          return _buildFinalSvgFallback(originalUrl, width, height, color);
        }

        return snapshot.data!;
      },
    );
  }

  /// Try SVG loading with fallback hierarchy for web
  static Future<Widget> _trySvgFallbackHierarchy(
    String originalUrl,
    String cleanUrl,
    String baseUrl,
    String activeTheme,
    double? width,
    double? height,
    Color? color,
    BoxFit? fit,
  ) async {
    // Additional safety check for empty URLs
    if (originalUrl.trim().isEmpty || cleanUrl.trim().isEmpty) {
      _logError(
        '_trySvgFallbackHierarchy called with empty URL: originalUrl=$originalUrl, cleanUrl=$cleanUrl',
      );
      return Future.value(
        _buildFinalSvgFallback(originalUrl, width, height, color),
      );
    }
    Widget buildSvgFromUrl(String url) {
      return SvgPicture.network(
        url,
        width: width,
        height: height,
        colorFilter:
            color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
        fit: fit ?? BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          _logError('SVG load failed for $url: $error');
          return _buildFinalSvgFallback(originalUrl, width, height, color);
        },
      );
    }

    // Step 1: Try active theme
    if (activeTheme != 'default') {
      final activeThemeUrl = '$baseUrl/$activeTheme/$cleanUrl';
      try {
        final response = await http.head(Uri.parse(activeThemeUrl));
        if (response.statusCode == 200) {
          _log('Loading SVG from active theme: $activeTheme');
          return Future.value(buildSvgFromUrl(activeThemeUrl));
        }
      } catch (e) {
        _logError('Active theme SVG check failed: $e');
      }
    }

    // Step 2: Try default theme
    final defaultThemeUrl = '$baseUrl/default/$cleanUrl';
    try {
      final response = await http.head(Uri.parse(defaultThemeUrl));
      if (response.statusCode == 200) {
        _log('Loading SVG from default theme');
        return Future.value(buildSvgFromUrl(defaultThemeUrl));
      }
    } catch (e) {
      _logError('Default theme SVG check failed: $e');
    }

    // Step 3: Try local asset
    try {
      _log('Attempting to load SVG from local asset: assets/$originalUrl');
      return Future.value(
        SvgPicture.asset(
          'assets/$originalUrl',
          width: width,
          height: height,
          colorFilter:
              color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
          fit: fit ?? BoxFit.contain,
          placeholderBuilder:
              (context) => Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: const CircularProgressIndicator(),
              ),
          errorBuilder: (context, error, stackTrace) {
            _logError('Asset SVG load failed: $error');
            return _buildFinalSvgFallback(originalUrl, width, height, color);
          },
        ),
      );
    } catch (e) {
      _logError('Exception loading asset SVG: $e');
      return Future.value(
        _buildFinalSvgFallback(originalUrl, width, height, color),
      );
    }
  }

  /// Final fallback for SVG
  static Widget _buildFinalSvgFallback(
    String url,
    double? width,
    double? height,
    Color? color,
  ) {
    try {
      _logError('Using final SVG fallback for: $url');
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey[600],
          size:
              (width != null && height != null)
                  ? (width < height ? width : height) * 0.5
                  : 24,
        ),
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  static Widget shimmerLoad() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: Get.width,
        height: 40,
        decoration: BoxDecoration(
          color: kColorBgLight,
          borderRadius: BorderRadius.circular(radiusSmall),
        ),
      ),
    );
  }
}
