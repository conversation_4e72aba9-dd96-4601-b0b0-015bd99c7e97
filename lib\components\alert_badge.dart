import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/components/alert_badge_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class AlertBadge extends StatelessWidget {
  AlertBadge({super.key});

  final AlertBadgeControllers controller = Get.put(AlertBadgeControllers());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(Routes.NOTIFICATION_PAGE),
      child: Obx(
        () => Stack(
          clipBehavior: Clip.none,
          children: [
            CircleAvatar(
              backgroundColor: kColorBgLight.withValues(alpha: 0.35),
              child: Utils.cachedSvgWrapper(
                'icon/notifikasi.svg',
                color: kColorBgLight,
              ),
            ),
            controller.count.value == 0
                ? SizedBox.shrink()
                : Positioned(
                  top: -4,
                  right: -4,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      '${controller.count.value}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
          ],
        ),
      ),
    );
  }
}
