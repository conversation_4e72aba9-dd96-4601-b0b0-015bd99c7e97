import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_upcoming_controller.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_past_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class PolisJatuhTempoController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Akan datang, 1 for Sudah Lewat
  bool isShowOtherAgent = false;
  final String? agentCode;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers for different tabs
  late PolisJatuhTempoUpcomingController upcomingController = Get.put(
    PolisJatuhTempoUpcomingController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  late PolisJatuhTempoPastController pastController = Get.put(
    PolisJatuhTempoPastController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  PolisJatuhTempoController({this.isShowOtherAgent = false, this.agentCode}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'polis_jatuh_tempo_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    _hasInitialized = true;
  }

  // Switch between sections
  void switchToUpcoming() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToPast() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on selected tab
  void refreshData() {
    // Only fetch data for the active tab
    if (selectedSection.value == 0) {
      // Tab "Akan Datang" - fetch upcoming data with type=UPCOMING
      upcomingController.fetchPolicyOverdueData();
    } else {
      // Tab "Sudah Lewat" - fetch past data with type=PAST
      pastController.fetchPolicyOverdueData();
    }
    setLoading(false);
  }
}
